<template>
  <div class="error-test">
    <h1>JsonTableContainer 错误修复测试</h1>
    
    <div class="test-info">
      <p>此页面用于测试修复 "Cannot read properties of undefined (reading 'clear')" 错误</p>
    </div>

    <div class="test-controls">
      <button @click="testClearAllData" class="test-btn">测试 clearAllData 方法</button>
      <button @click="testInsertWithClear" class="test-btn">测试带清空的数据插入</button>
      <button @click="testInsertWithoutClear" class="test-btn">测试不清空的数据插入</button>
    </div>

    <div class="result-display" v-if="testResult">
      <h3>测试结果：</h3>
      <div :class="['result-content', testResult.success ? 'success' : 'error']">
        <p><strong>状态:</strong> {{ testResult.success ? '成功' : '失败' }}</p>
        <p><strong>消息:</strong> {{ testResult.message }}</p>
        <pre v-if="testResult.error" class="error-details">{{ testResult.error }}</pre>
      </div>
    </div>

    <div class="table-container">
      <JsonTableContainer
        ref="testTable"
        :table-width="'100%'"
        :table-height="'300px'"
        :use-dynamic-header="true"
        :enable-nested-tables="true"
      />
    </div>
  </div>
</template>

<script>
import JsonTableContainer from '@/components/JsonTableContainer.vue'

export default {
  name: 'JsonTableErrorTest',
  components: {
    JsonTableContainer
  },
  data() {
    return {
      testResult: null
    }
  },
  methods: {
    testClearAllData() {
      try {
        console.log('测试 clearAllData 方法...')
        this.$refs.testTable.clearAllData()
        
        this.testResult = {
          success: true,
          message: 'clearAllData 方法执行成功，没有出现错误'
        }
        console.log('clearAllData 测试成功')
      } catch (error) {
        console.error('clearAllData 测试失败:', error)
        this.testResult = {
          success: false,
          message: 'clearAllData 方法执行失败',
          error: error.toString()
        }
      }
    },

    testInsertWithClear() {
      try {
        console.log('测试带清空的数据插入...')
        
        const testData = {
          "headerConfig": {
            "headers": [["项目", "内容", "状态"]],
            "merges": []
          },
          "headerWidthConfig": {
            "columnWidths": [150, 200, 100],
            "headerHeights": [40],
            "verticalHeaders": [false, false, false]
          },
          "cellRows": [
            [
              {"content": "测试项目", "originContent": "测试项目"},
              {"content": "测试内容", "originContent": "测试内容"},
              {"content": "测试中", "originContent": "测试中"}
            ]
          ]
        }

        const result = this.$refs.testTable.insertDataFromJSON(testData, {
          clearExisting: true,
          validateData: true
        })

        this.testResult = {
          success: result.success,
          message: result.success ? '带清空的数据插入成功' : '带清空的数据插入失败',
          error: result.success ? null : result.error
        }
        
        console.log('带清空的数据插入测试结果:', result)
      } catch (error) {
        console.error('带清空的数据插入测试失败:', error)
        this.testResult = {
          success: false,
          message: '带清空的数据插入执行失败',
          error: error.toString()
        }
      }
    },

    testInsertWithoutClear() {
      try {
        console.log('测试不清空的数据插入...')
        
        const testData = {
          "headerConfig": {
            "headers": [["新项目", "新内容", "新状态", "备注"]],
            "merges": []
          },
          "headerWidthConfig": {
            "columnWidths": [120, 180, 80, 120],
            "headerHeights": [40],
            "verticalHeaders": [false, false, false, false]
          },
          "cellRows": [
            [
              {"content": "新测试项目", "originContent": "新测试项目"},
              {"content": "新测试内容", "originContent": "新测试内容"},
              {"content": "进行中", "originContent": "进行中"},
              {"content": "重要", "originContent": "重要"}
            ]
          ]
        }

        const result = this.$refs.testTable.insertDataFromJSON(testData, {
          clearExisting: false,
          validateData: true
        })

        this.testResult = {
          success: result.success,
          message: result.success ? '不清空的数据插入成功' : '不清空的数据插入失败',
          error: result.success ? null : result.error
        }
        
        console.log('不清空的数据插入测试结果:', result)
      } catch (error) {
        console.error('不清空的数据插入测试失败:', error)
        this.testResult = {
          success: false,
          message: '不清空的数据插入执行失败',
          error: error.toString()
        }
      }
    }
  }
}
</script>

<style scoped>
.error-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

.test-info {
  background-color: #e7f3ff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.test-info p {
  margin: 0;
  color: #0056b3;
}

.test-controls {
  margin-bottom: 20px;
}

.test-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 16px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background-color: #218838;
}

.result-display {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.result-display h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.result-content {
  padding: 10px;
  border-radius: 4px;
}

.result-content.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.result-content.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.error-details {
  background: #f1f1f1;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 10px;
  white-space: pre-wrap;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
