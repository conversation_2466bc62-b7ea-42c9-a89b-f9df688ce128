<template>
  <div class="json-table-example">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>JsonTableContainer 组件示例</h1>
      <p class="description">
        JsonTableContainer 是基于 TableContainer 扩展的组件，支持嵌套表格功能。
        可以在单元格内嵌入子表格，支持多层嵌套（最多2层）。
      </p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- JSON数据输入区域 -->
      <div class="json-input-section">
        <h3>JSON数据操作</h3>
        <div class="json-controls">
          <!-- 上半部分：左右布局 -->
          <div class="top-section">
            <!-- 左侧：预设选择器 -->
            <div class="preset-selector">
              <div class="selector-group">
                <label for="preset-select">选择预设数据:</label>
                <select
                    id="preset-select"
                    v-model="selectedPreset"
                    @change="onPresetChange"
                    class="preset-select"
                >
                  <option value="">-- 请选择预设数据 --</option>
                  <option
                      v-for="option in presetOptions"
                      :key="option.value"
                      :value="option.value"
                      :title="option.description"
                  >
                    {{ option.label }}
                  </option>
                </select>
                <button @click="loadSelectedPreset" class="load-btn" :disabled="!selectedPreset">
                  加载数据
                </button>
                <button @click="clearJsonInput" class="clear-btn">
                  清空
                </button>
              </div>
              <div class="preset-description" v-if="selectedPresetInfo">
                <small>{{ selectedPresetInfo.description }}</small>
              </div>
              <div class="action-buttons">
                <button @click="insertJsonData" class="action-btn insert" :disabled="!jsonInput.trim()">
                  插入数据
                </button>
                <button @click="exportJsonData" class="action-btn export">
                  导出JSON
                </button>
                <button @click="validateJson" class="action-btn validate">
                  验证格式
                </button>
                <button @click="showDataFormatHelp" class="action-btn help">
                  数据格式说明
                </button>
              </div>
            </div>

            <!-- 右侧：选项和结果 -->
            <div class="right-section">
              <!-- 插入选项 -->
              <div class="json-options">
                <b style="font-size: 14px">插入选项</b>
                <div class="options-row">
                  <label class="option-item">
                    <input type="checkbox" v-model="insertOptions.clearExisting">
                    清空现有数据
                  </label>
                  <label class="option-item">
                    <input type="checkbox" v-model="insertOptions.validateData">
                    验证数据格式
                  </label>
                  <label class="option-item">
                    <input type="checkbox" v-model="insertOptions.includeNestedTables">
                    包含嵌套表格
                  </label>
                  <div class="option-item">
                    <label>开始行:</label>
                    <input
                        type="number"
                        v-model.number="insertOptions.startRow"
                        min="0"
                        class="number-input"
                    >
                  </div>
                </div>
              </div>

              <!-- 操作结果显示 -->
              <div class="result-section" v-if="operationResult">
                <b style="font-size: 14px">操作结果</b>
                <div class="result-content" :class="operationResult.success ? 'success' : 'error'">
                  <p><strong>状态:</strong> {{ operationResult.success ? '成功' : '失败' }}</p>
                  <p><strong>消息:</strong> {{ operationResult.message }}</p>
                  <p v-if="operationResult.insertedRows"><strong>插入行数:</strong> {{ operationResult.insertedRows }}</p>
                  <pre v-if="operationResult.error" class="error-details">{{ operationResult.error }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- 下半部分：JSON编辑器 -->
          <div class="json-editor">
            <h4>JSON数据编辑器</h4>
            <textarea
                v-model="jsonInput"
                placeholder="请输入JSON数据..."
                class="json-textarea"
                rows="12"
            ></textarea>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-section">
      <h3>JsonTableContainer 组件演示</h3>
      <div class="table-info">
        <span>嵌套层级: {{ nestedLevel }}</span>
        <span>最大嵌套层级: {{ maxNestedLevel }}</span>
        <span>启用嵌套表格: {{ enableNestedTables ? '是' : '否' }}</span>
      </div>
      <JsonTableContainer
          ref="jsonTableContainer"
          :table-width="'100%'"
          :table-height="'600px'"
          :data-rows="tableData"
          :nested-level="nestedLevel"
          :max-nested-level="maxNestedLevel"
          :enable-nested-tables="enableNestedTables"
          @data-inserted="handleDataInserted"
          @table-updated="handleTableUpdated"
          @nested-table-created="handleNestedTableCreated"
          @nested-table-updated="handleNestedTableUpdated"
          @nested-table-toggled="handleNestedTableToggled"
          @nested-table-removed="handleNestedTableRemoved"
      />
    </div>

    <!-- 数据格式说明对话框 -->
    <div v-if="showFormatHelpDialog" class="dialog-overlay" @click="closeFormatHelpDialog">
      <div class="dialog format-help-dialog" @click.stop>
        <h3>JsonTableContainer 数据格式说明</h3>
        <div class="dialog-content">
          <div class="format-section">
            <h4>基础数据结构</h4>
            <pre class="code-block">{{basicDataFormat}}</pre>
          </div>
          <div class="format-section">
            <h4>嵌套表格结构</h4>
            <pre class="code-block">{{nestedTableFormat}}</pre>
          </div>
          <div class="format-section">
            <h4>使用说明</h4>
            <ul class="help-list">
              <li>支持最多2层嵌套表格</li>
              <li>嵌套表格可以独立调整单元格大小</li>
              <li>支持展开/折叠嵌套表格</li>
              <li>完全兼容原有 TableContainer 数据格式</li>
              <li>可以通过右键菜单添加/移除嵌套表格</li>
            </ul>
          </div>
        </div>
        <div class="dialog-buttons">
          <button @click="closeFormatHelpDialog" class="btn-confirm">关闭</button>
        </div>
      </div>
    </div>

    <!-- JSON导出对话框 -->
    <div v-if="showExportDialog" class="dialog-overlay" @click="closeExportDialog">
      <div class="dialog export-dialog" @click.stop>
        <h3>导出JSON数据</h3>
        <div class="dialog-content">
          <div class="export-options">
            <label class="option-item">
              <input type="checkbox" v-model="exportOptions.includeEmpty">
              包含空行
            </label>
            <label class="option-item">
              <input type="checkbox" v-model="exportOptions.includeMergeInfo">
              包含合并信息
            </label>
            <label class="option-item">
              <input type="checkbox" v-model="exportOptions.includeNestedTables">
              包含嵌套表格
            </label>
          </div>
          <textarea
              ref="exportTextarea"
              v-model="exportedJson"
              class="export-textarea"
              rows="15"
              readonly
          ></textarea>
        </div>
        <div class="dialog-buttons">
          <button @click="copyExportedJson" class="btn-copy">复制到剪贴板</button>
          <button @click="closeExportDialog" class="btn-cancel">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import JsonTableContainer from '@/components/JsonTableContainer.vue'

export default {
  name: 'JsonTableExample',
  components: {
    JsonTableContainer
  },
  data() {
    return {
      // JSON输入
      jsonInput: '',

      // 预设数据选择
      selectedPreset: '',
      presetOptions: [
        {
          value: 'simple',
          label: '简单示例',
          description: '包含基础数据的简单表格示例'
        },
        {
          value: 'nested-simple',
          label: '简单嵌套表格',
          description: '包含一个简单嵌套表格的示例'
        },
        {
          value: 'nested-complex',
          label: '复杂嵌套表格',
          description: '包含多个嵌套表格和多层嵌套的复杂示例'
        },
        {
          value: 'inspection-record',
          label: '检验记录表',
          description: '实际的检验记录表格式，包含嵌套的检查项目'
        }
      ],

      // 表格数据
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 插入选项
      insertOptions: {
        clearExisting: true,
        validateData: true,
        includeNestedTables: true,
        startRow: 0
      },

      // 导出选项
      exportOptions: {
        includeEmpty: false,
        includeMergeInfo: true,
        includeNestedTables: true
      },

      // 操作结果
      operationResult: null,

      // 组件配置
      nestedLevel: 0,
      maxNestedLevel: 2,
      enableNestedTables: true,

      // 对话框状态
      showFormatHelpDialog: false,
      showExportDialog: false,
      exportedJson: '',

      // 数据格式示例
      basicDataFormat: `{
  "cellRows": [
    [
      {
        "content": "单元格内容",
        "originContent": "原始内容",
        "width": 150,
        "height": 50,
        "hasMath": false
      }
    ]
  ]
}`,
      nestedTableFormat: `{
  "cellRows": [
    [
      {
        "content": "主内容",
        "nestedTable": {
          "enabled": true,
          "config": {
            "headerConfig": {
              "headers": [["子列1", "子列2"]]
            },
            "cellRows": [
              [
                {"content": "子内容1"},
                {"content": "子内容2"}
              ]
            ]
          }
        }
      }
    ]
  ]
}`
    }
  },
  computed: {
    selectedPresetInfo() {
      return this.presetOptions.find(option => option.value === this.selectedPreset)
    }
  },
  methods: {
    // 预设数据相关方法
    onPresetChange() {
      if (this.selectedPreset) {
        this.loadSelectedPreset()
      }
    },

    loadSelectedPreset() {
      if (!this.selectedPreset) return

      const presetData = this.getPresetData(this.selectedPreset)
      if (presetData) {
        this.jsonInput = JSON.stringify(presetData, null, 2)
        this.operationResult = {
          success: true,
          message: `已加载预设数据: ${this.selectedPresetInfo.label}`
        }
      }
    },

    clearJsonInput() {
      this.jsonInput = ''
      this.operationResult = null
    },

    // JSON数据操作方法
    insertJsonData() {
      try {
        if (!this.jsonInput.trim()) {
          throw new Error('请输入JSON数据')
        }

        const jsonData = JSON.parse(this.jsonInput)
        const jsonTableContainer = this.$refs.jsonTableContainer

        if (!jsonTableContainer) {
          throw new Error('表格组件未找到')
        }

        const options = {
          clearExisting: this.insertOptions.clearExisting,
          startRow: this.insertOptions.startRow,
          validateData: this.insertOptions.validateData,
          includeNestedTables: this.insertOptions.includeNestedTables
        }

        const result = jsonTableContainer.insertDataFromJSON(jsonData, options)
        this.operationResult = result

        if (result.success) {
          this.tableData = jsonTableContainer.dataRows
        }

      } catch (error) {
        this.operationResult = {
          success: false,
          message: error.message || 'JSON数据插入失败',
          error: error.toString()
        }
      }
    },

    exportJsonData() {
      try {
        const jsonTableContainer = this.$refs.jsonTableContainer

        if (!jsonTableContainer) {
          throw new Error('表格组件未找到')
        }

        const exportedData = jsonTableContainer.getDataAsJSON(this.exportOptions)
        this.exportedJson = JSON.stringify(exportedData, null, 2)
        this.showExportDialog = true

        this.operationResult = {
          success: true,
          message: 'JSON数据导出成功'
        }

      } catch (error) {
        this.operationResult = {
          success: false,
          message: error.message || 'JSON数据导出失败',
          error: error.toString()
        }
      }
    },

    validateJson() {
      try {
        if (!this.jsonInput.trim()) {
          throw new Error('请输入JSON数据')
        }

        const jsonData = JSON.parse(this.jsonInput)
        const jsonTableContainer = this.$refs.jsonTableContainer

        if (!jsonTableContainer) {
          throw new Error('表格组件未找到')
        }

        const isValid = jsonTableContainer.validateJSONData(jsonData)

        this.operationResult = {
          success: isValid,
          message: isValid ? 'JSON数据格式验证通过' : 'JSON数据格式验证失败'
        }

      } catch (error) {
        this.operationResult = {
          success: false,
          message: 'JSON格式错误',
          error: error.toString()
        }
      }
    },

    // 对话框相关方法
    showDataFormatHelp() {
      this.showFormatHelpDialog = true
    },

    closeFormatHelpDialog() {
      this.showFormatHelpDialog = false
    },

    closeExportDialog() {
      this.showExportDialog = false
      this.exportedJson = ''
    },

    copyExportedJson() {
      if (this.$refs.exportTextarea) {
        this.$refs.exportTextarea.select()
        document.execCommand('copy')

        this.operationResult = {
          success: true,
          message: 'JSON数据已复制到剪贴板'
        }
      }
    },

    // 表格事件处理方法
    handleDataInserted(event) {
      console.log('数据插入事件:', event)
    },

    handleTableUpdated(event) {
      console.log('表格更新事件:', event)
    },

    handleNestedTableCreated(event) {
      console.log('嵌套表格创建事件:', event)
      this.operationResult = {
        success: true,
        message: `嵌套表格已创建在位置 (${event.rowIndex}, ${event.cellIndex})`
      }
    },

    handleNestedTableUpdated(event) {
      console.log('嵌套表格更新事件:', event)
    },

    handleNestedTableToggled(event) {
      console.log('嵌套表格切换事件:', event)
      const action = event.expanded ? '展开' : '折叠'
      this.operationResult = {
        success: true,
        message: `嵌套表格已${action} (${event.rowIndex}, ${event.cellIndex})`
      }
    },

    handleNestedTableRemoved(event) {
      console.log('嵌套表格移除事件:', event)
      this.operationResult = {
        success: true,
        message: `嵌套表格已移除 (${event.rowIndex}, ${event.cellIndex})`
      }
    },

    // 预设数据获取方法
    getPresetData(presetType) {
      switch (presetType) {
        case 'simple':
          return this.getSimplePresetData()
        case 'nested-simple':
          return this.getSimpleNestedPresetData()
        case 'nested-complex':
          return this.getComplexNestedPresetData()
        case 'inspection-record':
          return this.getInspectionRecordPresetData()
        default:
          return null
      }
    },

    getSimplePresetData() {
      return {
        "headerConfig": {
          "headers": [["项目", "内容", "状态", "备注"]],
          "merges": []
        },
        "headerWidthConfig": {
          "columnWidths": [120, 200, 100, 150],
          "headerHeights": [40],
          "verticalHeaders": [false, false, false, false]
        },
        "cellRows": [
          [
            {"content": "项目A", "originContent": "项目A", "width": 120, "height": 40},
            {"content": "这是项目A的详细内容", "originContent": "这是项目A的详细内容", "width": 200, "height": 40},
            {"content": "进行中", "originContent": "进行中", "width": 100, "height": 40},
            {"content": "需要关注", "originContent": "需要关注", "width": 150, "height": 40}
          ],
          [
            {"content": "项目B", "originContent": "项目B", "width": 120, "height": 40},
            {"content": "这是项目B的详细内容", "originContent": "这是项目B的详细内容", "width": 200, "height": 40},
            {"content": "已完成", "originContent": "已完成", "width": 100, "height": 40},
            {"content": "质量良好", "originContent": "质量良好", "width": 150, "height": 40}
          ]
        ],
        "metadata": {
          "title": "简单示例表格",
          "hasNestedTables": false
        }
      }
    },

    getSimpleNestedPresetData() {
      return {
        "headerConfig": {
          "headers": [["主项目", "详细信息", "状态"]],
          "merges": []
        },
        "headerWidthConfig": {
          "columnWidths": [150, 300, 100],
          "headerHeights": [40],
          "verticalHeaders": [false, false, false]
        },
        "cellRows": [
          [
            {"content": "项目管理", "originContent": "项目管理", "width": 150, "height": 80},
            {
              "content": "包含子项目详情",
              "originContent": "包含子项目详情",
              "width": 300,
              "height": 80,
              "nestedTable": {
                "enabled": true,
                "config": {
                  "headerConfig": {
                    "headers": [["子项目", "进度", "负责人"]],
                    "merges": []
                  },
                  "headerWidthConfig": {
                    "columnWidths": [100, 80, 80],
                    "headerHeights": [30],
                    "verticalHeaders": [false, false, false]
                  },
                  "cellRows": [
                    [
                      {"content": "需求分析", "originContent": "需求分析", "width": 100, "height": 25},
                      {"content": "90%", "originContent": "90%", "width": 80, "height": 25},
                      {"content": "张三", "originContent": "张三", "width": 80, "height": 25}
                    ],
                    [
                      {"content": "系统设计", "originContent": "系统设计", "width": 100, "height": 25},
                      {"content": "60%", "originContent": "60%", "width": 80, "height": 25},
                      {"content": "李四", "originContent": "李四", "width": 80, "height": 25}
                    ]
                  ],
                  "metadata": {
                    "title": "项目详情",
                    "level": 1
                  }
                }
              }
            },
            {"content": "进行中", "originContent": "进行中", "width": 100, "height": 80}
          ]
        ],
        "metadata": {
          "title": "简单嵌套表格示例",
          "hasNestedTables": true,
          "maxNestingLevel": 1
        }
      }
    },

    getComplexNestedPresetData() {
      return {
        "headerConfig": {
          "headers": [["部门", "项目信息", "状态", "备注"]],
          "merges": []
        },
        "headerWidthConfig": {
          "columnWidths": [120, 400, 80, 150],
          "headerHeights": [40],
          "verticalHeaders": [false, false, false, false]
        },
        "cellRows": [
          [
            {"content": "研发部", "originContent": "研发部", "width": 120, "height": 120},
            {
              "content": "多个项目管理",
              "originContent": "多个项目管理",
              "width": 400,
              "height": 120,
              "nestedTable": {
                "enabled": true,
                "config": {
                  "headerConfig": {
                    "headers": [["项目名称", "详细任务", "进度"]],
                    "merges": []
                  },
                  "headerWidthConfig": {
                    "columnWidths": [120, 200, 60],
                    "headerHeights": [30],
                    "verticalHeaders": [false, false, false]
                  },
                  "cellRows": [
                    [
                      {"content": "项目Alpha", "originContent": "项目Alpha", "width": 120, "height": 40},
                      {
                        "content": "包含多个任务",
                        "originContent": "包含多个任务",
                        "width": 200,
                        "height": 40,
                        "nestedTable": {
                          "enabled": true,
                          "config": {
                            "headerConfig": {
                              "headers": [["任务", "状态"]],
                              "merges": []
                            },
                            "headerWidthConfig": {
                              "columnWidths": [120, 60],
                              "headerHeights": [25],
                              "verticalHeaders": [false, false]
                            },
                            "cellRows": [
                              [
                                {"content": "前端开发", "originContent": "前端开发", "width": 120, "height": 20},
                                {"content": "80%", "originContent": "80%", "width": 60, "height": 20}
                              ],
                              [
                                {"content": "后端开发", "originContent": "后端开发", "width": 120, "height": 20},
                                {"content": "70%", "originContent": "70%", "width": 60, "height": 20}
                              ]
                            ],
                            "metadata": {
                              "title": "任务详情",
                              "level": 2
                            }
                          }
                        }
                      },
                      {"content": "75%", "originContent": "75%", "width": 60, "height": 40}
                    ]
                  ],
                  "metadata": {
                    "title": "研发项目",
                    "level": 1
                  }
                }
              }
            },
            {"content": "进行中", "originContent": "进行中", "width": 80, "height": 120},
            {"content": "重点关注", "originContent": "重点关注", "width": 150, "height": 120}
          ]
        ],
        "metadata": {
          "title": "复杂嵌套表格示例",
          "hasNestedTables": true,
          "maxNestingLevel": 2
        }
      }
    },

    getInspectionRecordPresetData() {
      return {
        "headerConfig": {
          "headers": [
            ["检查项目", "技术要求", "检查结果", "完工", "", "检查员", "组长", "检验员"],
            ["", "", "", "月", "日", "", "", ""]
          ],
          "merges": [
            { "startRow": 0, "startCol": 0, "endRow": 1, "endCol": 0, "content": "检查项目" },
            { "startRow": 0, "startCol": 1, "endRow": 1, "endCol": 1, "content": "技术要求" },
            { "startRow": 0, "startCol": 2, "endRow": 1, "endCol": 2, "content": "检查结果" },
            { "startRow": 0, "startCol": 3, "endRow": 0, "endCol": 4, "content": "完工" },
            { "startRow": 0, "startCol": 5, "endRow": 1, "endCol": 5, "content": "检查员" },
            { "startRow": 0, "startCol": 6, "endRow": 1, "endCol": 6, "content": "组长" },
            { "startRow": 0, "startCol": 7, "endRow": 1, "endCol": 7, "content": "检验员" }
          ]
        },
        "headerWidthConfig": {
          "columnWidths": [150, 250, 150, 50, 50, 80, 80, 80],
          "headerHeights": [50, 50],
          "verticalHeaders": [false, false, false, false, false, true, true, true]
        },
        "cellRows": [
          [
            {"content": "基础检查", "originContent": "基础检查", "width": 150, "height": 80},
            {
              "content": "详细技术要求",
              "originContent": "详细技术要求",
              "width": 250,
              "height": 80,
              "nestedTable": {
                "enabled": true,
                "config": {
                  "headerConfig": {
                    "headers": [["检查点", "标准", "方法"]],
                    "merges": []
                  },
                  "headerWidthConfig": {
                    "columnWidths": [80, 100, 60],
                    "headerHeights": [25],
                    "verticalHeaders": [false, false, false]
                  },
                  "cellRows": [
                    [
                      {"content": "尺寸", "originContent": "尺寸", "width": 80, "height": 20},
                      {"content": "±0.1mm", "originContent": "±0.1mm", "width": 100, "height": 20},
                      {"content": "卡尺", "originContent": "卡尺", "width": 60, "height": 20}
                    ],
                    [
                      {"content": "表面", "originContent": "表面", "width": 80, "height": 20},
                      {"content": "Ra1.6", "originContent": "Ra1.6", "width": 100, "height": 20},
                      {"content": "目测", "originContent": "目测", "width": 60, "height": 20}
                    ]
                  ],
                  "metadata": {
                    "title": "检查要求详情",
                    "level": 1
                  }
                }
              }
            },
            {"content": "合格", "originContent": "合格", "width": 150, "height": 80},
            {"content": "12", "originContent": "12", "width": 50, "height": 80},
            {"content": "15", "originContent": "15", "width": 50, "height": 80},
            {"content": "张三", "originContent": "张三", "width": 80, "height": 80},
            {"content": "李四", "originContent": "李四", "width": 80, "height": 80},
            {"content": "王五", "originContent": "王五", "width": 80, "height": 80}
          ]
        ],
        "metadata": {
          "title": "检验记录表",
          "hasNestedTables": true,
          "maxNestingLevel": 1
        }
      }
    }
  }
}
</script>

<style scoped>
/* 页面整体样式 */
.json-table-example {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.page-header .description {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  max-width: 800px;
  margin: 0 auto;
}

/* 控制面板样式 */
.control-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.json-input-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.top-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.preset-selector {
  flex: 1;
}

.right-section {
  flex: 1;
}

.selector-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.preset-select {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.load-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.load-btn {
  background-color: #007bff;
  color: white;
}

.load-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.clear-btn {
  background-color: #6c757d;
  color: white;
}

.preset-description {
  color: #666;
  font-style: italic;
  margin-bottom: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn.insert {
  background-color: #28a745;
  color: white;
}

.action-btn.export {
  background-color: #17a2b8;
  color: white;
}

.action-btn.validate {
  background-color: #ffc107;
  color: #212529;
}

.action-btn.help {
  background-color: #6f42c1;
  color: white;
}

.action-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* 选项和结果区域 */
.json-options {
  background: white;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.options-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.number-input {
  width: 60px;
  padding: 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.result-section {
  background: white;
  padding: 15px;
  border-radius: 4px;
}

.result-content {
  padding: 10px;
  border-radius: 4px;
}

.result-content.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.result-content.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.error-details {
  background: #f1f1f1;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 10px;
}

/* JSON编辑器 */
.json-editor h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.json-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
}

/* 表格区域 */
.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-section h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.table-info {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  max-width: 90%;
  max-height: 90%;
  overflow: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.format-help-dialog {
  min-width: 600px;
}

.export-dialog {
  min-width: 500px;
}

.dialog h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.format-section {
  margin-bottom: 20px;
}

.format-section h4 {
  margin: 0 0 10px 0;
  color: #555;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

.help-list {
  margin: 0;
  padding-left: 20px;
}

.help-list li {
  margin-bottom: 5px;
  color: #555;
}

.export-options {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.export-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background-color: #f8f9fa;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-copy {
  background-color: #28a745;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-copy:hover {
  background-color: #218838;
}

.btn-cancel {
  background-color: #6c757d;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-cancel:hover {
  background-color: #5a6268;
}

.btn-confirm {
  background-color: #007bff;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-confirm:hover {
  background-color: #0056b3;
}
</style>
