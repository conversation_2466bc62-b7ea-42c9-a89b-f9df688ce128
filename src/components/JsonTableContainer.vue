<template>
  <div class="json-table-container-wrapper" :data-nested-level="nestedLevel || 0">
    <!-- 表格容器 -->
    <div class="table-wrapper">
      <div class="table-container" ref="tableContainer" :style="tableContainerStyle">
        <div class="table-scroll-container">
          <table ref="editableTable" :style="tableStyle" @contextmenu="handleContextMenu">
            <!-- 动态表头 -->
            <tr v-for="(headerRow, headerRowIndex) in currentHeaderConfig.headers" :key="'header-' + headerRowIndex">
              <td
                v-for="(headerCell, headerCellIndex) in headerRow"
                :key="'header-' + headerRowIndex + '-' + headerCellIndex"
                v-show="!isHeaderCellHidden(headerRowIndex, headerCellIndex)"
                class="header-cell"
                :rowspan="getHeaderCellRowspan(headerRowIndex, headerCellIndex)"
                :colspan="getHeaderCellColspan(headerRowIndex, headerCellIndex)"
                :title="getHeaderCellTitle(headerRowIndex, headerCellIndex)"
                :style="getHeaderCellStyle(headerRowIndex, headerCellIndex)"
                :class="getHeaderCellClass(headerRowIndex, headerCellIndex)"
              >
                <span
                  v-if="shouldUseVerticalText(headerCellIndex)"
                  class="vertical-text-span"
                >
                  {{ getHeaderCellContent(headerRowIndex, headerCellIndex) }}
                </span>
                <span v-else>
                  {{ getHeaderCellContent(headerRowIndex, headerCellIndex) }}
                </span>
              </td>
            </tr>
            <!-- 数据行 - 支持嵌套表格 -->
            <tr v-for="(row, rowIndex) in dataRows" :key="rowIndex">
              <td
                v-for="(cell, cellIndex) in row"
                :key="cellIndex"
                v-show="!isCellHidden(rowIndex, cellIndex)"
                class="editable-cell"
                :class="{
                  'has-math': cell.hasMath,
                  'merged-cell': cell.merged,
                  'has-nested-table': hasNestedTable(cell)
                }"
                :style="getMergedCellStyle(rowIndex, cellIndex)"
                :rowspan="getCellRowspan(cell)"
                :colspan="getCellColspan(cell)"
                @contextmenu="handleCellContextMenu(rowIndex, cellIndex, $event)"
              >
                <!-- 嵌套表格渲染 -->
                <div v-if="hasNestedTable(cell)" class="nested-table-container">
                  <!-- 主内容区域 -->
                  <div class="main-content-area">
                    <CellEditor
                      :ref="`cell-${rowIndex}-${cellIndex}`"
                      :content="cell.content"
                      :has-math="cell.hasMath"
                      :auto-focus="cell.isEditing"
                      :select-all="cell.selectAll"
                      :height="30"
                      :min-height="30"
                      @start-edit="handleCellStartEdit(rowIndex, cellIndex)"
                      @finish-edit="handleCellFinishEdit(rowIndex, cellIndex, $event)"
                      @cancel-edit="handleCellCancelEdit(rowIndex, cellIndex)"
                      @content-change="handleCellContentChange(rowIndex, cellIndex, $event)"
                      @input="handleCellInput(rowIndex, cellIndex, $event)"
                      @move-next="handleCellMoveNext(rowIndex, cellIndex, $event)"
                    />
                  </div>

                  <!-- 嵌套表格内容 - 简化版无表头 -->
                  <div class="nested-table-content">
                    <table class="nested-table" :style="getNestedTableStyle(cell)">
                      <tr v-for="(nestedRow, nestedRowIndex) in getNestedTableDataRows(cell)" :key="nestedRowIndex">
                        <td
                          v-for="(nestedCell, nestedCellIndex) in nestedRow"
                          :key="nestedCellIndex"
                          class="nested-cell"
                          :style="getNestedCellStyle(cell, nestedCellIndex)"
                        >
                          <CellEditor
                            :ref="`nested-cell-${rowIndex}-${cellIndex}-${nestedRowIndex}-${nestedCellIndex}`"
                            :content="nestedCell.content"
                            :has-math="nestedCell.hasMath"
                            :auto-focus="nestedCell.isEditing"
                            :select-all="nestedCell.selectAll"
                            :height="25"
                            :min-height="25"
                            @start-edit="handleNestedCellStartEdit(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex)"
                            @finish-edit="handleNestedCellFinishEdit(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex, $event)"
                            @cancel-edit="handleNestedCellCancelEdit(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex)"
                            @content-change="handleNestedCellContentChange(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex, $event)"
                            @input="handleNestedCellInput(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex, $event)"
                          />
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>

                <!-- 普通单元格编辑器 -->
                <CellEditor
                  v-else
                  :ref="`cell-${rowIndex}-${cellIndex}`"
                  :content="cell.content"
                  :has-math="cell.hasMath"
                  :auto-focus="cell.isEditing"
                  :select-all="cell.selectAll"
                  :height="getCellEditorHeight(rowIndex, cellIndex)"
                  :min-height="getCellEditorMinHeight(rowIndex, cellIndex)"
                  @start-edit="handleCellStartEdit(rowIndex, cellIndex)"
                  @finish-edit="handleCellFinishEdit(rowIndex, cellIndex, $event)"
                  @cancel-edit="handleCellCancelEdit(rowIndex, cellIndex)"
                  @content-change="handleCellContentChange(rowIndex, cellIndex, $event)"
                  @input="handleCellInput(rowIndex, cellIndex, $event)"
                  @move-next="handleCellMoveNext(rowIndex, cellIndex, $event)"
                />
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div class="context-menu-info">
        <span class="info-label">当前列宽:</span>
        <span class="info-value">{{ getCurrentColumnWidthForDisplay() }}px</span>
      </div>
      <div class="context-menu-divider"></div>
      <div
        class="context-menu-item"
        @click="handleRowHeightClick"
        @mousedown.stop
      >
        调整此行高度
      </div>
      <div
        class="context-menu-item"
        @click="handleColumnWidthClick"
        @mousedown.stop
      >
        调整此列宽度
      </div>
      <!-- 嵌套表格相关菜单项 -->
      <div v-if="isDataRow && canAddNestedTable()" class="context-menu-divider"></div>
      <div
        v-if="isDataRow && canAddNestedTable()"
        class="context-menu-item"
        @click="handleAddNestedTableClick"
        @mousedown.stop
      >
        添加嵌套表格
      </div>
      <div
        v-if="isDataRow && hasNestedTableInCurrentCell()"
        class="context-menu-item"
        @click="handleRemoveNestedTableClick"
        @mousedown.stop
      >
        移除嵌套表格
      </div>
      <div class="context-menu-divider"></div>
      <div
        v-if="isDataRow"
        class="context-menu-item delete-item"
        @click="handleDeleteRowClick"
        @mousedown.stop
      >
        删除此行
      </div>
    </div>

    <!-- 行高调整对话框 -->
    <div v-if="rowHeightDialogVisible" class="dialog-overlay" @click="closeRowHeightDialog">
      <div class="dialog" @click.stop>
        <h3>调整行高度</h3>
        <div class="dialog-content">
          <label>当前行高度: {{ currentRowHeight }}px</label>
          <input
            type="number"
            v-model="newRowHeight"
            :min="minCellHeight"
            placeholder="输入新的行高度(px)"
            @keydown.enter="applyRowHeight"
            @keydown.esc="closeRowHeightDialog"
            ref="rowHeightInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeRowHeightDialog" class="btn-cancel">取消</button>
          <button @click="applyRowHeight" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 列宽调整对话框 -->
    <div v-if="columnWidthDialogVisible" class="dialog-overlay" @click="closeColumnWidthDialog">
      <div class="dialog" @click.stop>
        <h3>调整列宽度</h3>
        <div class="dialog-content">
          <label>当前列宽度: {{ currentColumnWidth }}px</label>
          <input
            type="number"
            v-model="newColumnWidth"
            :min="minCellWidth"
            placeholder="输入新的列宽度(px)"
            @keydown.enter="applyColumnWidth"
            @keydown.esc="closeColumnWidthDialog"
            ref="columnWidthInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeColumnWidthDialog" class="btn-cancel">取消</button>
          <button @click="applyColumnWidth" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 添加嵌套表格对话框 -->
    <div v-if="addNestedTableDialogVisible" class="dialog-overlay" @click="closeAddNestedTableDialog">
      <div class="dialog nested-table-dialog" @click.stop>
        <h3>添加嵌套表格</h3>
        <div class="dialog-content">
          <div class="form-group">
            <label>表格标题:</label>
            <input
              type="text"
              v-model="nestedTableForm.title"
              placeholder="输入嵌套表格标题"
              ref="nestedTableTitleInput"
            >
          </div>
          <div class="form-group">
            <label>列数:</label>
            <input
              type="number"
              v-model.number="nestedTableForm.columns"
              :min="1"
              :max="10"
              placeholder="列数"
            >
          </div>
          <div class="form-group">
            <label>行数:</label>
            <input
              type="number"
              v-model.number="nestedTableForm.rows"
              :min="1"
              :max="20"
              placeholder="行数"
            >
          </div>
        </div>
        <div class="dialog-buttons">
          <button @click="closeAddNestedTableDialog" class="btn-cancel">取消</button>
          <button @click="createNestedTable" class="btn-confirm">创建</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CellEditor from './CellEditor.vue'

export default {
  name: 'JsonTableContainer',
  components: {
    CellEditor
  },
  props: {
    // 继承 TableContainer 的所有 props
    tableWidth: {
      type: String,
      default: '1600px'
    },
    tableHeight: {
      type: String,
      default: '600px'
    },
    dataRows: {
      type: Array,
      default: () => [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ]
    },
    minCellWidth: {
      type: Number,
      default: 20
    },
    minCellHeight: {
      type: Number,
      default: 20
    },
    headerCellWidth: {
      type: Number,
      default: 120
    },
    headerCellHeight: {
      type: Number,
      default: 50
    },
    columnWidths: {
      type: Array,
      default: () => [150, 200, 150, 50, 50, 80, 80, 80]
    },
    verticalHeadersConfig: {
      type: Array,
      default: () => [false, false, false, false, false, true, true, true]
    },
    headerConfig: {
      type: Object,
      default: null
    },
    headerWidthConfig: {
      type: Object,
      default: null
    },
    useDynamicHeader: {
      type: Boolean,
      default: false
    },

    // 新增：嵌套表格相关 props
    nestedLevel: {
      type: Number,
      default: 0
    },
    maxNestedLevel: {
      type: Number,
      default: 2
    },
    enableNestedTables: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 继承 TableContainer 的所有 data
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextCell: null,
      isDataRow: false,
      currentRowIndex: -1,
      currentColumnIndex: -1,
      contextMenuColumnIndex: -1,

      rowHeightDialogVisible: false,
      currentRowHeight: 0,
      newRowHeight: 0,

      columnWidthDialogVisible: false,
      currentColumnWidth: 0,
      newColumnWidth: 0,

      internalColumnWidths: [],
      internalRowHeights: [],
      internalUseDynamicHeader: false,
      internalHeaderConfig: null,
      internalHeaderWidthConfig: null,
      internalVerticalHeadersConfig: null,
      internalDataRows: null,

      mathJaxReady: false,

      // 新增：嵌套表格相关状态
      addNestedTableDialogVisible: false,
      nestedTableForm: {
        title: '',
        columns: 3,
        rows: 2
      },
      currentNestedTablePosition: null // 当前操作的嵌套表格位置
    }
  },
  computed: {
    // 继承 TableContainer 的所有 computed 属性
    tableContainerStyle() {
      const style = {
        width: this.tableWidth
      }

      if (this.tableHeight !== 'auto') {
        style.height = this.tableHeight
        style.maxHeight = this.tableHeight
      }

      return style
    },
    tableStyle() {
      const totalWidth = this.currentColumnWidths.reduce((sum, width) => sum + width, 0)
      console.log('tableStyle 计算:', {
        currentColumnWidths: this.currentColumnWidths,
        totalWidth: totalWidth,
        internalUseDynamicHeader: this.internalUseDynamicHeader,
        useDynamicHeader: this.useDynamicHeader
      })
      return {
        width: `${totalWidth}px`,
        height: 'auto'
      }
    },
    currentColumnWidths() {
      if (this.internalColumnWidths.length > 0) {
        return this.internalColumnWidths
      }

      if (this.currentHeaderWidthConfig && this.currentHeaderWidthConfig.columnWidths) {
        return this.currentHeaderWidthConfig.columnWidths
      }

      return this.columnWidths
    },
    currentHeaderConfig() {
      const useDynamic = this.internalUseDynamicHeader || this.useDynamicHeader
      const headerConfig = this.internalHeaderConfig || this.headerConfig

      if (useDynamic && headerConfig) {
        return headerConfig
      }
      return {
        headers: [
          ['检查项目', '技术要求', '检查结果', '完工', '', '检查员', '组长', '检验员'],
          ['', '', '', '月', '日', '', '', '']
        ],
        merges: [
          {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '检查项目'},
          {startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '技术要求'},
          {startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '检查结果'},
          {startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '完工'},
          {startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: '检查员'},
          {startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: '组长'},
          {startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: '检验员'}
        ]
      }
    },
    currentHeaderWidthConfig() {
      const useDynamic = this.internalUseDynamicHeader || this.useDynamicHeader
      const widthConfig = this.internalHeaderWidthConfig || this.headerWidthConfig

      if (useDynamic && widthConfig) {
        return widthConfig
      }
      return {
        columnWidths: [150, 200, 150, 50, 50, 80, 80, 80],
        headerHeights: [50, 50],
        verticalHeaders: [false, false, false, false, false, true, true, true]
      }
    },
    currentVerticalHeadersConfig() {
      return this.internalVerticalHeadersConfig || this.verticalHeadersConfig
    },
    currentColumnCount() {
      if (this.currentHeaderConfig && this.currentHeaderConfig.headers && this.currentHeaderConfig.headers.length > 0) {
        const firstHeaderRow = this.currentHeaderConfig.headers[0]
        return firstHeaderRow ? firstHeaderRow.length : 8
      }
      return 8
    },

    // 新增：嵌套表格相关 computed 属性

    // 获取数据单元格的样式
    getDataCellStyle() {
      return (columnIndex) => {
        const width = this.currentColumnWidths[columnIndex] || this.headerCellWidth
        return {
          minWidth: `${width}px !important`,
          width: `${width}px !important`,
          maxWidth: `${width}px !important`,
          boxSizing: 'border-box'
        }
      }
    },

    // 获取合并单元格的样式
    getMergedCellStyle() {
      return (rowIndex, cellIndex) => {
        const baseStyle = this.getDataCellStyle(cellIndex)
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]

        if (cell && cell.merged) {
          const {rowspan, colspan} = cell.merged

          if (colspan > 1) {
            let totalWidth = 0
            for (let i = cellIndex; i < cellIndex + colspan && i < this.currentColumnWidths.length; i++) {
              totalWidth += this.currentColumnWidths[i] || this.headerCellWidth
            }
            baseStyle.minWidth = `${totalWidth}px !important`
            baseStyle.width = `${totalWidth}px !important`
            baseStyle.maxWidth = `${totalWidth}px !important`
          }

          if (rowspan > 1) {
            const baseHeight = 50
            const totalHeight = baseHeight * rowspan
            baseStyle.minHeight = `${totalHeight}px !important`
            baseStyle.height = `${totalHeight}px !important`
          }

          baseStyle.backgroundColor = '#f8f9fa'
          baseStyle.border = '2px solid #007bff'
          baseStyle.verticalAlign = 'top'
          baseStyle.padding = '0'
        }

        // 嵌套表格单元格的特殊样式
        if (this.hasNestedTable(cell)) {
          baseStyle.padding = '4px'
          baseStyle.verticalAlign = 'top'
          baseStyle.position = 'relative'
        }

        return baseStyle
      }
    },

    // 获取单元格的rowspan
    getCellRowspan() {
      return (cell) => {
        return cell && cell.merged && cell.merged.rowspan > 1 ? cell.merged.rowspan : null
      }
    },

    // 获取单元格的colspan
    getCellColspan() {
      return (cell) => {
        return cell && cell.merged && cell.merged.colspan > 1 ? cell.merged.colspan : null
      }
    },

    // 判断数据行单元格是否被隐藏
    isCellHidden() {
      return (dataRowIndex, cellIndex) => {
        for (let r = 0; r < this.dataRows.length; r++) {
          const row = this.dataRows[r]
          if (!row) continue

          for (let c = 0; c < row.length; c++) {
            const cell = row[c]
            if (cell && cell.merged && !(r === dataRowIndex && c === cellIndex)) {
              const {startRow, startCol, endRow, endCol} = cell.merged
              if (dataRowIndex >= startRow && dataRowIndex <= endRow &&
                  cellIndex >= startCol && cellIndex <= endCol) {
                return true
              }
            }
          }
        }
        return false
      }
    },

    // 获取单元格编辑器的高度
    getCellEditorHeight() {
      return (rowIndex, cellIndex) => {
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
        if (cell && cell.merged) {
          const {rowspan} = cell.merged
          const baseHeight = 50
          return baseHeight * rowspan
        }
        return 'auto'
      }
    },

    // 获取单元格编辑器的最小高度
    getCellEditorMinHeight() {
      return (rowIndex, cellIndex) => {
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
        if (cell && cell.merged) {
          const {rowspan} = cell.merged
          const baseMinHeight = 50
          return baseMinHeight * rowspan
        }
        return 50
      }
    },

    // 表头相关 computed 方法

    // 检查表头单元格是否被隐藏
    isHeaderCellHidden() {
      return (headerRowIndex, headerCellIndex) => {
        if (!this.currentHeaderConfig.merges) return false

        for (const merge of this.currentHeaderConfig.merges) {
          if (headerRowIndex >= merge.startRow && headerRowIndex <= merge.endRow &&
              headerCellIndex >= merge.startCol && headerCellIndex <= merge.endCol &&
              !(headerRowIndex === merge.startRow && headerCellIndex === merge.startCol)) {
            return true
          }
        }
        return false
      }
    },

    // 获取表头单元格的行跨度
    getHeaderCellRowspan() {
      return (headerRowIndex, headerCellIndex) => {
        if (!this.currentHeaderConfig.merges) return 1

        for (const merge of this.currentHeaderConfig.merges) {
          if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex) {
            return merge.endRow - merge.startRow + 1
          }
        }
        return 1
      }
    },

    // 获取表头单元格的列跨度
    getHeaderCellColspan() {
      return (headerRowIndex, headerCellIndex) => {
        if (!this.currentHeaderConfig.merges) return 1

        for (const merge of this.currentHeaderConfig.merges) {
          if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex) {
            return merge.endCol - merge.startCol + 1
          }
        }
        return 1
      }
    },

    // 获取表头单元格的标题
    getHeaderCellTitle() {
      return (headerRowIndex, headerCellIndex) => {
        return this.getHeaderCellContent(headerRowIndex, headerCellIndex)
      }
    },

    // 获取表头单元格的内容
    getHeaderCellContent() {
      return (headerRowIndex, headerCellIndex) => {
        if (this.currentHeaderConfig.merges) {
          for (const merge of this.currentHeaderConfig.merges) {
            if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex && merge.content) {
              return merge.content
            }
          }
        }

        const headerRow = this.currentHeaderConfig.headers[headerRowIndex]
        return headerRow ? (headerRow[headerCellIndex] || '') : ''
      }
    },

    // 获取表头单元格的样式
    getHeaderCellStyle() {
      return (headerRowIndex, headerCellIndex) => {
        const widthConfig = this.currentHeaderWidthConfig
        const width = widthConfig.columnWidths[headerCellIndex] || 150
        const height = widthConfig.headerHeights[headerRowIndex] || 50

        return {
          minWidth: `${width}px !important`,
          width: `${width}px !important`,
          height: `${height}px !important`,
          minHeight: `${height}px !important`
        }
      }
    },

    // 获取表头单元格的CSS类
    getHeaderCellClass() {
      return (headerRowIndex, headerCellIndex) => {
        const isVertical = this.currentVerticalHeadersConfig[headerCellIndex] || false
        return {
          'vertical-text': isVertical,
          'horizontal-text': !isVertical
        }
      }
    },

    // 判断是否应该使用纵向文字
    shouldUseVerticalText() {
      return (headerCellIndex) => {
        return this.currentVerticalHeadersConfig[headerCellIndex] || false
      }
    }
  },
  methods: {
    // ========== 嵌套表格相关方法 ==========

    // 检查单元格是否有嵌套表格
    hasNestedTable(cell) {
      return cell && cell.nestedTable && cell.nestedTable.enabled === true
    },

    // 获取嵌套表格样式
    getNestedTableStyle(cell) {
      if (!this.hasNestedTable(cell)) return {}

      const config = cell.nestedTable.config
      if (config && config.columnWidths) {
        const totalWidth = config.columnWidths.reduce((sum, width) => sum + width, 0)
        return {
          width: `${totalWidth}px`,
          borderCollapse: 'collapse',
          marginTop: '4px'
        }
      }

      return {
        width: '100%',
        borderCollapse: 'collapse',
        marginTop: '4px'
      }
    },

    // 获取嵌套单元格样式
    getNestedCellStyle(cell, cellIndex) {
      if (!this.hasNestedTable(cell)) return {}

      const config = cell.nestedTable.config
      let width = 80 // 默认宽度

      if (config && config.columnWidths && config.columnWidths[cellIndex]) {
        width = config.columnWidths[cellIndex]
      }

      return {
        width: `${width}px`,
        minWidth: `${width}px`,
        border: '1px solid #ddd',
        padding: '2px'
      }
    },

    // 获取嵌套表格的数据行 - 简化版
    getNestedTableDataRows(cell) {
      if (!this.hasNestedTable(cell)) return []

      const config = cell.nestedTable.config
      if (config && config.cellRows) {
        // 简化的数据格式，直接返回可编辑的单元格数据
        return config.cellRows.map(row =>
          row.map(cellData => ({
            content: cellData.content || '',
            originContent: cellData.originContent || cellData.content || '',
            isEditing: false,
            originalContent: cellData.content || '',
            hasMath: cellData.hasMath || false,
            selectAll: false
          }))
        )
      }

      return []
    },

    // ========== 嵌套单元格编辑处理方法 ==========

    // 处理嵌套单元格开始编辑
    handleNestedCellStartEdit(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.isEditing = true
          this.$emit('nested-cell-start-edit', {
            parentRowIndex,
            parentCellIndex,
            nestedRowIndex,
            nestedCellIndex,
            nestedCell
          })
        }
      }
    },

    // 处理嵌套单元格完成编辑
    handleNestedCellFinishEdit(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex, newContent) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.isEditing = false
          nestedCell.content = newContent || ''
          nestedCell.hasMath = this.containsMath(nestedCell.content)

          this.$emit('nested-cell-finish-edit', {
            parentRowIndex,
            parentCellIndex,
            nestedRowIndex,
            nestedCellIndex,
            nestedCell,
            newContent
          })
        }
      }
    },

    // 处理嵌套单元格取消编辑
    handleNestedCellCancelEdit(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.isEditing = false
          this.$emit('nested-cell-cancel-edit', {
            parentRowIndex,
            parentCellIndex,
            nestedRowIndex,
            nestedCellIndex,
            nestedCell
          })
        }
      }
    },

    // 处理嵌套单元格内容变化
    handleNestedCellContentChange(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex, newContent) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.content = newContent || ''
          nestedCell.hasMath = this.containsMath(nestedCell.content)
        }
      }
    },

    // 处理嵌套单元格输入
    handleNestedCellInput(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex, content) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.content = content || ''
        }
      }
    },

    // 获取嵌套表格的表头配置
    getNestedTableHeaderConfig(cell) {
      if (!this.hasNestedTable(cell)) return null

      const config = cell.nestedTable.config
      return config && config.headerConfig ? config.headerConfig : {
        headers: [['列1', '列2', '列3']],
        merges: []
      }
    },

    // 获取嵌套表格的宽度配置
    getNestedTableWidthConfig(cell) {
      if (!this.hasNestedTable(cell)) return null

      const config = cell.nestedTable.config
      return config && config.headerWidthConfig ? config.headerWidthConfig : {
        columnWidths: [100, 100, 100],
        headerHeights: [30],
        verticalHeaders: [false, false, false]
      }
    },

    // 检查当前单元格是否可以添加嵌套表格
    canAddNestedTable() {
      if (!this.enableNestedTables) return false
      if (this.nestedLevel >= this.maxNestedLevel) return false

      const cell = this.getCurrentContextCell()
      return cell && !this.hasNestedTable(cell)
    },

    // 检查当前单元格是否有嵌套表格
    hasNestedTableInCurrentCell() {
      const cell = this.getCurrentContextCell()
      return this.hasNestedTable(cell)
    },

    // 获取当前右键菜单对应的单元格
    getCurrentContextCell() {
      if (this.currentRowIndex >= 0 && this.currentColumnIndex >= 0) {
        const dataRowIndex = this.currentRowIndex - (this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0)
        if (dataRowIndex >= 0 && dataRowIndex < this.dataRows.length) {
          return this.dataRows[dataRowIndex][this.currentColumnIndex]
        }
      }
      return null
    },

    // 处理嵌套表格更新事件
    handleNestedTableUpdated(rowIndex, cellIndex, event) {
      console.log('嵌套表格更新:', rowIndex, cellIndex, event)
      this.$emit('nested-table-updated', {rowIndex, cellIndex, event})
    },

    // 处理嵌套表格数据插入事件
    handleNestedDataInserted(rowIndex, cellIndex, event) {
      console.log('嵌套表格数据插入:', rowIndex, cellIndex, event)
      this.$emit('nested-data-inserted', {rowIndex, cellIndex, event})
    },

    // 显示添加嵌套表格对话框
    showAddNestedTableDialog() {
      this.currentNestedTablePosition = {
        rowIndex: this.currentRowIndex - (this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0),
        cellIndex: this.currentColumnIndex
      }

      this.nestedTableForm = {
        title: '嵌套表格',
        columns: 3,
        rows: 2
      }

      this.addNestedTableDialogVisible = true
      this.contextMenuVisible = false

      this.$nextTick(() => {
        if (this.$refs.nestedTableTitleInput) {
          this.$refs.nestedTableTitleInput.focus()
        }
      })
    },

    // 关闭添加嵌套表格对话框
    closeAddNestedTableDialog() {
      this.addNestedTableDialogVisible = false
      this.currentNestedTablePosition = null
    },

    // 创建嵌套表格 - 简化版
    createNestedTable() {
      if (!this.currentNestedTablePosition) return

      const {rowIndex, cellIndex} = this.currentNestedTablePosition
      const {title, columns, rows} = this.nestedTableForm

      // 确保行存在
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (!cell) return

      // 创建简化的嵌套表格配置（无表头）
      const nestedTableConfig = {
        enabled: true,
        config: {
          columnWidths: Array(columns).fill(100), // 简化的列宽配置
          cellRows: Array(rows).fill(null).map(() =>
            Array(columns).fill(null).map(() => ({
              content: '',
              originContent: '',
              isEditing: false,
              hasMath: false
            }))
          ),
          metadata: {
            title: title,
            level: (this.nestedLevel || 0) + 1,
            parentCell: {row: rowIndex, col: cellIndex},
            columns: columns,
            rows: rows
          }
        }
      }

      // 使用 Vue.set 确保响应式更新
      this.$set(cell, 'nestedTable', nestedTableConfig)

      this.closeAddNestedTableDialog()
      this.$emit('nested-table-created', {rowIndex, cellIndex, config: nestedTableConfig})
    },

    // 移除嵌套表格 - 简化版
    removeNestedTable() {
      if (!this.currentNestedTablePosition) return

      const {rowIndex, cellIndex} = this.currentNestedTablePosition
      const cell = this.dataRows[rowIndex][cellIndex]

      if (cell && this.hasNestedTable(cell)) {
        // 移除嵌套表格配置
        this.$delete(cell, 'nestedTable')

        this.contextMenuVisible = false
        this.$emit('nested-table-removed', {rowIndex, cellIndex})
      }
    },

    // ========== 右键菜单相关方法 ==========

    // 处理右键菜单
    handleContextMenu(e) {
      e.preventDefault()

      const cell = e.target.closest('td')
      if (!cell) return

      this.currentContextCell = cell
      this.contextMenuX = e.pageX
      this.contextMenuY = e.pageY
      this.contextMenuVisible = true

      this.isDataRow = cell.classList.contains('editable-cell')
      this.calculateCellPosition(cell)
      this.getCurrentRowHeight()
      this.getCurrentColumnWidth()

      this.$nextTick(() => {
        this.adjustContextMenuPosition()
      })
    },

    // 计算单元格位置
    calculateCellPosition(cell) {
      const table = this.$refs.editableTable
      if (!table) {
        console.log('calculateCellPosition: table not found')
        return
      }

      const rows = Array.from(table.rows)
      const rowIndex = rows.findIndex(row => Array.from(row.cells).includes(cell))
      this.currentRowIndex = rowIndex

      console.log('calculateCellPosition开始:', {
        cell: cell,
        cellClasses: cell.className,
        rowIndex: rowIndex,
        totalRows: rows.length,
        isHeaderCell: cell.classList.contains('header-cell'),
        isEditableCell: cell.classList.contains('editable-cell')
      })

      if (rowIndex >= 0) {
        let columnIndex = 0
        const row = rows[rowIndex]
        const cells = Array.from(row.cells)
        const cellIndex = cells.indexOf(cell)

        console.log('计算列索引:', {
          cellIndex: cellIndex,
          totalCells: cells.length
        })

        for (let i = 0; i < cellIndex; i++) {
          const colspan = parseInt(cells[i].getAttribute('colspan') || '1')
          columnIndex += colspan
          console.log(`第${i}个单元格colspan=${colspan}, 累计columnIndex=${columnIndex}`)
        }

        this.currentColumnIndex = columnIndex
        this.contextMenuColumnIndex = columnIndex

        console.log(`最终设置: currentColumnIndex=${columnIndex}, contextMenuColumnIndex=${columnIndex}`)
      } else {
        this.currentColumnIndex = 0
        this.contextMenuColumnIndex = 0
        console.log('行索引计算失败，使用默认值0')
      }
    },

    // 获取当前行高度
    getCurrentRowHeight() {
      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        if (table && table.rows[this.currentRowIndex]) {
          const row = table.rows[this.currentRowIndex]
          const rect = row.getBoundingClientRect()
          this.currentRowHeight = Math.round(rect.height)
        } else {
          this.currentRowHeight = 50
        }
      } else {
        this.currentRowHeight = 50
      }
    },

    // 获取当前列宽度
    getCurrentColumnWidth() {
      if (this.currentContextCell) {
        const rect = this.currentContextCell.getBoundingClientRect()
        this.currentColumnWidth = Math.round(rect.width)
      } else {
        this.currentColumnWidth = 120
      }
    },

    // 获取当前列宽度用于显示
    getCurrentColumnWidthForDisplay() {
      if (this.contextMenuColumnIndex >= 0 && this.contextMenuColumnIndex < this.currentColumnWidths.length) {
        return this.currentColumnWidths[this.contextMenuColumnIndex]
      }
      return this.headerCellWidth
    },

    // 调整右键菜单位置
    adjustContextMenuPosition() {
      const menu = document.querySelector('.context-menu')
      if (!menu) return

      const menuRect = menu.getBoundingClientRect()
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      if (this.contextMenuX + menuRect.width > windowWidth) {
        this.contextMenuX = windowWidth - menuRect.width - 10
      }

      if (this.contextMenuY + menuRect.height > windowHeight) {
        this.contextMenuY = windowHeight - menuRect.height - 10
      }
    },

    // 处理右键菜单项点击
    handleRowHeightClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showRowHeightDialog()
    },

    handleColumnWidthClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showColumnWidthDialog()
    },

    handleAddNestedTableClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showAddNestedTableDialog()
    },

    handleRemoveNestedTableClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.currentNestedTablePosition = {
        rowIndex: this.currentRowIndex - (this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0),
        cellIndex: this.currentColumnIndex
      }
      this.removeNestedTable()
    },

    handleDeleteRowClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.deleteCurrentRow()
    },

    // ========== 继承 TableContainer 的核心方法 ==========

    // 设置事件监听器
    setupEventListeners() {
      document.addEventListener('mousedown', this.handleDocumentClick)
    },

    // 移除事件监听器
    removeEventListeners() {
      document.removeEventListener('mousedown', this.handleDocumentClick)
    },

    // 处理文档点击（关闭菜单）
    handleDocumentClick(e) {
      if (!this.contextMenuVisible) return
      if (e.target.closest('.context-menu')) return
      this.contextMenuVisible = false
    },

    // 显示行高调整对话框
    showRowHeightDialog() {
      this.contextMenuVisible = false
      this.newRowHeight = this.currentRowHeight || 50
      this.rowHeightDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.rowHeightInput) {
          this.$refs.rowHeightInput.focus()
          this.$refs.rowHeightInput.select()
        }
      })
    },

    // 显示列宽调整对话框
    showColumnWidthDialog() {
      this.contextMenuVisible = false
      this.newColumnWidth = this.currentColumnWidth || 120
      this.columnWidthDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.columnWidthInput) {
          this.$refs.columnWidthInput.focus()
          this.$refs.columnWidthInput.select()
        }
      })
    },

    // 关闭对话框
    closeRowHeightDialog() {
      this.rowHeightDialogVisible = false
    },

    closeColumnWidthDialog() {
      this.columnWidthDialogVisible = false
    },

    // 应用行高调整
    applyRowHeight() {
      const height = Math.max(this.minCellHeight, parseInt(this.newRowHeight) || this.minCellHeight)

      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        const targetRow = table.rows[this.currentRowIndex]
        const cells = Array.from(targetRow.cells)

        cells.forEach(cell => {
          cell.style.height = `${height}px`
          cell.style.minHeight = `${height}px`
        })

        const dataRowIndex = this.currentRowIndex - (this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0)
        if (dataRowIndex >= 0) {
          this.$set(this.internalRowHeights, dataRowIndex, height)
        }
      }

      this.closeRowHeightDialog()
      this.$emit('table-updated')
    },

    // 应用列宽调整
    applyColumnWidth() {
      const newWidth = Math.max(this.minCellWidth, parseInt(this.newColumnWidth) || this.minCellWidth)

      if (this.currentColumnIndex >= 0) {
        this.updateColumnWidth(this.currentColumnIndex, newWidth)
      }

      this.closeColumnWidthDialog()
      this.$emit('table-updated')
    },

    // 更新指定列的宽度
    updateColumnWidth(columnIndex, newWidth) {
      console.log(`更新列宽: 列${columnIndex} 新宽度${newWidth}px`)
      console.log('当前状态:', {
        internalUseDynamicHeader: this.internalUseDynamicHeader,
        internalColumnWidthsLength: this.internalColumnWidths.length,
        currentHeaderWidthConfig: this.currentHeaderWidthConfig
      })

      // 如果使用动态表头配置，需要更新动态配置
      if (this.internalUseDynamicHeader && this.internalHeaderWidthConfig && this.internalHeaderWidthConfig.columnWidths) {
        console.log('更新动态表头配置中的列宽')
        // 确保动态配置的列宽数组存在且长度足够
        if (columnIndex >= 0 && columnIndex < this.internalHeaderWidthConfig.columnWidths.length) {
          // 更新动态配置中的列宽
          this.$set(this.internalHeaderWidthConfig.columnWidths, columnIndex, newWidth)
          console.log('动态配置更新成功:', this.internalHeaderWidthConfig.columnWidths)
        }
      } else {
        // 使用传统的内部列宽更新方式
        console.log('更新内部列宽配置')
        // 确保内部列宽数组有足够的长度
        if (this.internalColumnWidths.length === 0) {
          this.internalColumnWidths = [...this.currentColumnWidths]
        }

        if (columnIndex >= 0 && columnIndex < this.internalColumnWidths.length) {
          this.$set(this.internalColumnWidths, columnIndex, newWidth)
          console.log('内部配置更新成功:', this.internalColumnWidths)
        }
      }

      // 发射事件通知父组件更新
      this.$emit('column-width-changed', {
        columnIndex,
        newWidth,
        columnWidths: [...this.currentColumnWidths],
        useDynamicHeader: this.internalUseDynamicHeader
      })
    },

    // 删除当前行
    deleteCurrentRow() {
      if (this.isDataRow && this.currentRowIndex >= 2) {
        const dataRowIndex = this.currentRowIndex - 2

        if (dataRowIndex >= 0 && dataRowIndex < this.dataRows.length) {
          if (confirm('确定要删除这一行吗？此操作不可撤销。')) {
            this.$emit('delete-row', dataRowIndex)
            this.contextMenuVisible = false
          }
        }
      }
    },

    // ========== 单元格编辑相关方法 ==========

    // 处理单元格右键菜单
    handleCellContextMenu(rowIndex, cellIndex, event) {
      this.handleContextMenu(event)
    },

    // 处理单元格开始编辑
    handleCellStartEdit(rowIndex, cellIndex) {
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = true
        this.$emit('start-edit', {rowIndex, cellIndex, cell})
      }
    },

    // 处理单元格完成编辑
    handleCellFinishEdit(rowIndex, cellIndex, newContent) {
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = false
        cell.content = newContent || ''
        cell.hasMath = this.containsMath(cell.content)

        this.$emit('finish-edit', {rowIndex, cellIndex, cell, newContent})
      }
    },

    // 处理单元格取消编辑
    handleCellCancelEdit(rowIndex, cellIndex) {
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = false
        this.$emit('cancel-edit', {rowIndex, cellIndex, cell})
      }
    },

    // 处理单元格内容变化
    handleCellContentChange(rowIndex, cellIndex, newContent) {
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = newContent || ''
        cell.hasMath = this.containsMath(cell.content)
      }
    },

    // 处理单元格输入
    handleCellInput(rowIndex, cellIndex, content) {
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = content || ''
      }
    },

    // 处理单元格移动到下一个
    handleCellMoveNext(rowIndex, cellIndex, direction) {
      let nextRowIndex = rowIndex
      let nextCellIndex = cellIndex

      if (direction === 'next') {
        nextCellIndex++
        if (nextCellIndex >= this.currentColumnCount) {
          nextCellIndex = 0
          nextRowIndex++
        }
      } else if (direction === 'prev') {
        nextCellIndex--
        if (nextCellIndex < 0) {
          nextCellIndex = this.currentColumnCount - 1
          nextRowIndex--
        }
      }

      if (nextRowIndex >= 0) {
        this.ensureRowExists(nextRowIndex)

        this.$nextTick(() => {
          const nextCellRef = this.$refs[`cell-${nextRowIndex}-${nextCellIndex}`]
          if (nextCellRef && nextCellRef[0]) {
            nextCellRef[0].edit()
          }
        })
      }
    },

    // 确保行存在
    ensureRowExists(rowIndex) {
      while (this.dataRows.length <= rowIndex) {
        const newRow = Array(this.currentColumnCount).fill(null).map(() => ({
          content: '',
          originContent: '',
          isEditing: false,
          originalContent: '',
          hasMath: false,
          selectAll: false
        }))
        this.dataRows.push(newRow)
        this.$emit('ensure-row', this.dataRows.length - 1)
      }

      this.ensureRowColumnCount(rowIndex)
    },

    // 确保指定行的列数与当前表头一致
    ensureRowColumnCount(rowIndex) {
      if (rowIndex >= 0 && rowIndex < this.dataRows.length) {
        const row = this.dataRows[rowIndex]
        const targetColumnCount = this.currentColumnCount

        while (row.length < targetColumnCount) {
          row.push({
            content: '',
            isEditing: false,
            originalContent: '',
            hasMath: false,
            selectAll: false
          })
        }

        if (row.length > targetColumnCount) {
          row.splice(targetColumnCount)
        }
      }
    },

    // 确保所有现有行的列数与当前表头一致
    ensureAllRowsColumnCount() {
      for (let i = 0; i < this.dataRows.length; i++) {
        this.ensureRowColumnCount(i)
      }
    },

    // 检测内容是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 设置动态表头配置
    setDynamicHeaderConfig(useDynamic, headerConfig, headerWidthConfig, verticalHeadersConfig) {
      this.internalUseDynamicHeader = useDynamic
      this.internalHeaderConfig = headerConfig
      this.internalHeaderWidthConfig = headerWidthConfig
      this.internalVerticalHeadersConfig = verticalHeadersConfig

      if (useDynamic) {
        this.internalColumnWidths = []
      }

      this.$nextTick(() => {
        this.ensureAllRowsColumnCount()
      })
    },

    // ========== JSON 数据处理方法 ==========

    /**
     * 从JSON数据插入表格数据（支持嵌套表格）
     * @param {Object} jsonData - JSON数据对象
     * @param {Object} options - 插入选项
     */
    insertDataFromJSON(jsonData, options = {}) {
      try {
        const {
          clearExisting = false,
          startRow = 0,
          mergeCells = [],
          validateData = true
        } = options

        // 验证JSON数据格式
        if (validateData && !this.validateJSONData(jsonData)) {
          throw new Error('JSON数据格式不正确')
        }

        // 清空现有数据（如果需要）
        if (clearExisting) {
          this.clearAllData()
        }

        // 设置动态表头配置（如果存在）
        if (jsonData.headerConfig || jsonData.headerWidthConfig) {
          console.log('设置动态表头配置:', {
            headerConfig: jsonData.headerConfig,
            headerWidthConfig: jsonData.headerWidthConfig,
            currentUseDynamicHeader: this.useDynamicHeader
          })

          this.setDynamicHeaderConfig(
            true, // 使用动态表头
            jsonData.headerConfig || null,
            jsonData.headerWidthConfig || null,
            jsonData.headerWidthConfig?.verticalHeaders || null
          )

          // 强制更新以确保表格重新渲染
          this.$nextTick(() => {
            this.$forceUpdate()
            console.log('动态表头配置设置完成，当前列宽:', this.currentColumnWidths)
          })
        }

        // 插入数据行（使用cellRows格式，支持嵌套表格）
        if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
          this.insertCellRowsFromJSON(jsonData.cellRows, startRow)
        }

        // 应用单元格合并
        if (mergeCells && mergeCells.length > 0) {
          this.applyCellMerges(mergeCells)
        }

        // 确保所有行的列数与表头一致
        this.ensureAllRowsColumnCount()

        // 强制更新表格以确保样式正确应用
        this.$nextTick(() => {
          this.$forceUpdate()
          console.log('insertDataFromJSON 完成，当前状态:', {
            currentColumnWidths: this.currentColumnWidths,
            dataRowsLength: this.dataRows.length,
            firstRowLength: this.dataRows[0] ? this.dataRows[0].length : 0
          })
        })

        // 更新表格信息
        this.$emit('table-updated')
        this.$emit('data-inserted', {jsonData, options})

        return {
          success: true,
          message: '数据插入成功',
          insertedRows: jsonData.cellRows ? jsonData.cellRows.length : 0
        }

      } catch (error) {
        console.error('JSON数据插入失败:', error)
        return {
          success: false,
          message: error.message || '数据插入失败',
          error: error
        }
      }
    },

    /**
     * 验证JSON数据格式（支持嵌套表格）
     * @param {Object} jsonData - 要验证的JSON数据
     * @returns {boolean} 验证结果
     */
    validateJSONData(jsonData) {
      if (!jsonData || typeof jsonData !== 'object') {
        return false
      }

      // 检查是否有cellRows数组
      if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
        return this.validateCellRowsData(jsonData.cellRows)
      }

      return false
    },

    /**
     * 验证复杂cellRows数据格式（支持嵌套表格）
     */
    validateCellRowsData(cellRows) {
      for (const row of cellRows) {
        if (!Array.isArray(row) || row.length > this.currentColumnCount) {
          return false
        }

        // 检查每个单元格数据对象
        for (const cellData of row) {
          if (!cellData || typeof cellData !== 'object') {
            return false
          }

          // content字段是必需的
          if (!cellData.hasOwnProperty('content')) {
            return false
          }

          // 验证嵌套表格配置
          if (cellData.nestedTable) {
            if (!this.validateNestedTableConfig(cellData.nestedTable)) {
              return false
            }
          }
        }
      }
      return true
    },

    /**
     * 验证嵌套表格配置
     */
    validateNestedTableConfig(nestedTable) {
      if (!nestedTable || typeof nestedTable !== 'object') {
        return false
      }

      // 检查必需字段
      if (typeof nestedTable.enabled !== 'boolean') {
        return false
      }

      if (nestedTable.enabled && nestedTable.config) {
        const config = nestedTable.config

        // 验证嵌套表格的基本结构
        if (config.cellRows && Array.isArray(config.cellRows)) {
          // 递归验证嵌套表格的数据（但限制嵌套层级）
          if (this.nestedLevel < this.maxNestedLevel) {
            return this.validateCellRowsData(config.cellRows)
          }
        }
      }

      return true
    },

    /**
     * 从JSON数据插入复杂格式的行（支持嵌套表格）
     * @param {Array} cellRows - 单元格数据数组
     * @param {number} startRow - 开始插入的行索引
     */
    insertCellRowsFromJSON(cellRows, startRow = 0) {
      cellRows.forEach((rowData, index) => {
        const targetRowIndex = startRow + index
        this.ensureRowExists(targetRowIndex)

        // 填充行数据，确保每行的单元格数与当前表头一致
        for (let cellIndex = 0; cellIndex < this.currentColumnCount; cellIndex++) {
          const cellData = cellIndex < rowData.length ? rowData[cellIndex] : {}
          const content = cellData.content || ''
          const originContent = cellData.originContent || content

          const cell = this.dataRows[targetRowIndex][cellIndex]
          if (cell) {
            // 使用Vue.set确保响应式更新
            this.$set(cell, 'content', content)
            this.$set(cell, 'originContent', originContent)
            this.$set(cell, 'hasMath', cellData.hasMath || this.containsMath(content))
            this.$set(cell, 'isEditing', false)
            this.$set(cell, 'originalContent', content)

            // 处理嵌套表格数据
            if (cellData.nestedTable && cellData.nestedTable.enabled) {
              this.$set(cell, 'nestedTable', cellData.nestedTable)

              // 注意：简化版嵌套表格始终显示，不需要展开状态管理
            }

            // 设置尺寸属性
            if (cellData.width) {
              this.$set(cell, 'width', cellData.width)
            }
            if (cellData.height) {
              this.$set(cell, 'height', cellData.height)
              this.$set(this.internalRowHeights, targetRowIndex, cellData.height)
            }

            // 清除可能存在的合并标记
            if (cell.merged) {
              this.$delete(cell, 'merged')
            }
          }
        }
      })

      console.log('复杂格式数据插入完成（支持嵌套表格），行数:', cellRows.length)
    },

    /**
     * 获取表格数据为JSON格式（支持嵌套表格）
     * @param {Object} options - 导出选项
     * @returns {Object} JSON格式的表格数据
     */
    getDataAsJSON(options = {}) {
      const {
        includeEmpty = false,
        includeMergeInfo = true,
        includeNestedTables = true
      } = options

      const result = {
        headerConfig: {
          headers: this.currentHeaderConfig.headers,
          merges: this.currentHeaderConfig.merges || []
        },
        headerWidthConfig: {
          columnWidths: this.currentHeaderWidthConfig?.columnWidths || this.currentColumnWidths,
          headerHeights: this.currentHeaderWidthConfig?.headerHeights || [],
          verticalHeaders: this.currentVerticalHeadersConfig || this.verticalHeadersConfig
        },
        cellRows: [],
        merges: [],
        metadata: {
          title: this.internalHeaderConfig?.title || '检验记录表',
          useDynamicHeader: this.internalUseDynamicHeader || this.useDynamicHeader,
          hasCustomWidth: true,
          totalRows: this.dataRows.length,
          totalColumns: this.currentColumnCount,
          headerRows: this.currentHeaderConfig.headers.length,
          exportTime: new Date().toISOString(),
          hasNestedTables: includeNestedTables,
          nestedLevel: this.nestedLevel || 0,
          maxNestedLevel: this.maxNestedLevel
        }
      }

      // 导出行数据为cellRows格式（包含嵌套表格）
      this.dataRows.forEach((row, rowIndex) => {
        // 如果不包含空行，跳过完全空的行
        if (!includeEmpty && row.every(cell => !(cell.content || '').trim() && !this.hasNestedTable(cell))) {
          return
        }

        const cellRowData = row.map((cell, cellIndex) => {
          const cellData = {
            content: cell.content || '',
            originContent: cell.originContent || cell.content || '',
            width: this.currentColumnWidths[cellIndex] || this.headerCellWidth || 100,
            height: this.getActualRowHeight ? this.getActualRowHeight(rowIndex) : 50
          }

          // 如果有数学公式相关信息，添加到cellData中
          if (cell.hasMath) {
            cellData.hasMath = cell.hasMath
          }

          // 如果有嵌套表格，添加到cellData中
          if (includeNestedTables && this.hasNestedTable(cell)) {
            cellData.nestedTable = {
              enabled: true,
              config: this.exportNestedTableConfig(cell.nestedTable.config)
            }
          }

          return cellData
        })

        result.cellRows.push(cellRowData)
      })

      return result
    },

    /**
     * 导出嵌套表格配置
     */
    exportNestedTableConfig(config) {
      if (!config) return null

      return {
        headerConfig: config.headerConfig || {headers: [], merges: []},
        headerWidthConfig: config.headerWidthConfig || {columnWidths: [], headerHeights: [], verticalHeaders: []},
        cellRows: config.cellRows || [],
        merges: config.merges || [],
        metadata: config.metadata || {}
      }
    },

    /**
     * 清空所有数据
     */
    clearAllData() {
      this.dataRows.splice(0, this.dataRows.length)

      // 添加一个空行
      const emptyRow = Array(this.currentColumnCount).fill(null).map(() => ({
        content: '',
        originContent: '',
        isEditing: false,
        originalContent: '',
        hasMath: false,
        selectAll: false
      }))
      this.dataRows.push(emptyRow)

      // 注意：简化版嵌套表格不需要清除展开状态，因为没有展开/折叠功能
    },

    // 获取实际的行高度
    getActualRowHeight(dataRowIndex) {
      const table = this.$refs.editableTable
      if (!table) return 50

      const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0
      const actualRowIndex = dataRowIndex + headerRowCount

      if (actualRowIndex < table.rows.length) {
        const row = table.rows[actualRowIndex]
        const rect = row.getBoundingClientRect()
        const height = Math.round(rect.height)

        if (!this.internalRowHeights[dataRowIndex] || this.internalRowHeights[dataRowIndex] !== height) {
          this.$set(this.internalRowHeights, dataRowIndex, height)
        }

        return height
      }

      return this.internalRowHeights[dataRowIndex] || 50
    }
  },
  mounted() {
    this.setupEventListeners()

    if (!this.internalUseDynamicHeader) {
      this.internalColumnWidths = [...this.columnWidths]
    }
  },
  beforeDestroy() {
    this.removeEventListeners()
  }
}
</script>

<style scoped>
/* 继承 TableContainer 的基础样式 */
.json-table-container-wrapper {
  position: relative;
}

.table-wrapper {
  width: 100%;
  overflow: auto;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #70a7cb rgba(255, 255, 255, 0.1);
}

/* Webkit浏览器滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(0deg, #70a7cb, #5a8db3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(0deg, #5a8db3, #4a7da3);
}

.table-container {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  border-radius: 8px;
  overflow: auto;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.table-scroll-container {
  overflow: auto;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #70a7cb rgba(255, 255, 255, 0.1);
}

/* 表格滚动容器的Webkit浏览器滚动条样式 */
.table-scroll-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background: linear-gradient(0deg, #70a7cb, #5a8db3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(0deg, #5a8db3, #4a7da3);
}

table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
}

/* 表头样式 */
.header-cell {
  background: linear-gradient(90deg, #4373b3 0%, rgba(13, 67, 141, 0.4) 100%);
  background-repeat: no-repeat;
  background-position: left;
  background-size: contain;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px;
  text-align: center;
  font-weight: bold;
  position: relative;
  vertical-align: middle;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.header-cell.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.vertical-text-span {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  display: inline-block;
  white-space: nowrap;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 数据单元格样式 */
.editable-cell {
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
  vertical-align: top;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(2px);
}

.editable-cell.has-math {
  background: rgba(0, 153, 255, 0.1);
  border: 1px solid rgba(0, 153, 255, 0.3);
}

.editable-cell.merged-cell {
  background: rgba(0, 123, 255, 0.1);
  border: 2px solid rgba(0, 123, 255, 0.5);
  backdrop-filter: blur(5px);
}

/* 嵌套表格样式 - 简化版 */
.editable-cell.has-nested-table {
  background-color: #fafafa;
  border: 2px solid #28a745;
  padding: 4px;
  vertical-align: top;
}

.nested-table-container {
  width: 100%;
  height: 100%;
}

.main-content-area {
  margin-bottom: 4px;
}

.nested-table-content {
  margin-top: 4px;
  border: 1px solid rgba(40, 167, 69, 0.5);
  border-radius: 6px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nested-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  overflow: hidden;
}

.nested-cell {
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 4px;
  vertical-align: top;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(1px);
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  transition: all 0.2s ease;
}

.nested-cell:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.25);
}

/* 嵌套表格中的数学公式单元格 */
.nested-cell.has-math {
  background: rgba(0, 153, 255, 0.08);
  border: 1px solid rgba(0, 153, 255, 0.2);
}

/* 嵌套表格的滚动条样式 */
.nested-table-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.nested-table-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.nested-table-content::-webkit-scrollbar-thumb {
  background: linear-gradient(0deg, rgba(112, 167, 203, 0.8), rgba(90, 141, 179, 0.8));
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.nested-table-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(0deg, rgba(90, 141, 179, 0.9), rgba(74, 125, 163, 0.9));
}

/* 嵌套表格层级样式 - 根据嵌套层级调整样式 */
.json-table-container-wrapper[data-nested-level="1"] .nested-table-content {
  border-color: rgba(40, 167, 69, 0.6);
  background: rgba(255, 255, 255, 0.12);
}

.json-table-container-wrapper[data-nested-level="1"] .nested-cell {
  background: rgba(255, 255, 255, 0.04);
  font-size: 11px;
}

.json-table-container-wrapper[data-nested-level="2"] .nested-table-content {
  border-color: rgba(255, 193, 7, 0.6);
  background: rgba(255, 255, 255, 0.08);
}

.json-table-container-wrapper[data-nested-level="2"] .nested-cell {
  background: rgba(255, 255, 255, 0.02);
  font-size: 10px;
  padding: 2px;
}

/* 嵌套表格的CellEditor样式调整 */
.nested-table-content .cell-editor {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.nested-table-content .cell-editor input,
.nested-table-content .cell-editor textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
}

.nested-table-content .cell-editor input::placeholder,
.nested-table-content .cell-editor textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 嵌套表格的展开/折叠动画 */
.nested-table-content {
  animation: nestedTableSlideIn 0.3s ease-out;
  transform-origin: top;
}

@keyframes nestedTableSlideIn {
  from {
    opacity: 0;
    transform: scaleY(0);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: linear-gradient(135deg, rgba(18, 75, 154, 0.95), rgba(13, 67, 141, 0.95));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 180px;
}

.context-menu-info {
  padding: 10px 14px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.context-menu-info .info-label {
  color: rgba(255, 255, 255, 0.8);
}

.context-menu-info .info-value {
  color: #17d2ef;
  font-weight: bold;
}

.context-menu-item {
  padding: 10px 14px;
  cursor: pointer;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.context-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  color: #17d2ef;
}

.context-menu-item.delete-item {
  color: #ff6b6b;
}

.context-menu-item.delete-item:hover {
  background: rgba(255, 107, 107, 0.2);
  color: #ff4757;
}

.context-menu-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  margin: 4px 0;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.dialog {
  background: linear-gradient(135deg, rgba(18, 75, 154, 0.9), rgba(13, 67, 141, 0.9));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 24px;
  min-width: 320px;
  max-width: 520px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dialog.nested-table-dialog {
  min-width: 420px;
}

.dialog h3 {
  margin: 0 0 20px 0;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #17d2ef 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dialog-content {
  margin-bottom: 24px;
}

.dialog-content label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.dialog-content input {
  width: 100%;
  padding: 10px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  border: 1px solid #3E66C2;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  backdrop-filter: blur(5px);
  box-sizing: border-box;
}

.dialog-content input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.dialog-content .number-input {
  width: 100px;
}

.form-group {
  margin-bottom: 18px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.form-group input {
  width: 100%;
  padding: 10px;
  background: linear-gradient(0deg, #0D3A8D, #03256B);
  border: 1px solid #3E66C2;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  backdrop-filter: blur(5px);
  box-sizing: border-box;
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 18px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  color: #ffffff;
}

.btn-cancel {
  background: linear-gradient(0deg, #6c757d, #495057);
  border: 1px solid #6c757d;
}

.btn-cancel:hover {
  background: linear-gradient(0deg, #495057, #343a40);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.btn-confirm {
  background: linear-gradient(0deg, #0096FF, #043475);
  border: 1px solid #548DDB;
}

.btn-confirm:hover {
  background: linear-gradient(0deg, #043475, #032a5c);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 150, 255, 0.3);
}
</style>
